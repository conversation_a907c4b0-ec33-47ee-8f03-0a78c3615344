<?= $this->extend('admin/promis_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects') ?>">Projects</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects/' . $project['id']) ?>">Project Profile</a></li>
            <li class="breadcrumb-item active" aria-current="page">Project Documents</li>
        </ol>
    </nav>

    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?= $page_title ?></h1>
            <p class="text-muted mb-0">Project: <?= esc($project['title']) ?> (<?= esc($project['pro_code']) ?>)</p>
        </div>
        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Upload Document
        </a>
    </div>

    <!-- Document Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Documents</h5>
                            <h3 class="mb-0"><?= $documentStats['total_documents'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-file-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Recent Uploads</h5>
                            <h3 class="mb-0"><?= $documentStats['recent_uploads'] ?? 0 ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-upload fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Document Types</h5>
                            <h3 class="mb-0"><?= count($documentStats['by_type'] ?? []) ?></h3>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tags fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="GET" action="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>">
                <div class="row">
                    <div class="col-md-4">
                        <label for="category" class="form-label">Category</label>
                        <select name="category" id="category" class="form-select">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $key => $label): ?>
                                <option value="<?= $key ?>" <?= ($filters['category'] == $key) ? 'selected' : '' ?>>
                                    <?= esc($label) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               placeholder="Search by title, filename, or description..." 
                               value="<?= esc($filters['search'] ?? '') ?>">
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">Filter</button>
                        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary">Clear</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Documents List -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Project Documents</h5>
        </div>
        <div class="card-body">
            <?php if (empty($documents)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No documents found</h5>
                    <p class="text-muted">Upload your first project document to get started.</p>
                    <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Upload Document
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Description</th>
                                <th>Category</th>
                                <th>File Path</th>
                                <th>Version</th>
                                <th>Upload Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($documents as $document): ?>
                                <tr>
                                    <td>
                                        <?php if (!empty($document['description'])): ?>
                                            <strong><?= esc($document['description']) ?></strong>
                                        <?php else: ?>
                                            <em class="text-muted">No description</em>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($document['doc_type'])): ?>
                                            <span class="badge bg-secondary">
                                                <?= esc($categories[$document['doc_type']] ?? $document['doc_type']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-light text-dark">Unspecified</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <i class="fas fa-file"></i> <?= esc(basename($document['doc_path'])) ?>
                                    </td>
                                    <td>v<?= $document['version_no'] ?? 1 ?></td>
                                    <td><?= date('M j, Y g:i A', strtotime($document['created_at'])) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Download">
                                                <i class="fas fa-download"></i>
                                            </a>
                                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>" 
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="confirmDelete(<?= $document['id'] ?>, '<?= esc($document['description'] ?? 'Document') ?>')" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the document "<span id="documentTitle"></span>"?</p>
                <p class="text-danger"><strong>Warning:</strong> This action cannot be undone and will permanently delete the file from the server.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">Delete Document</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(documentId, documentDescription) {
    document.getElementById('documentTitle').textContent = documentDescription || 'this document';
    document.getElementById('deleteForm').action = '<?= base_url('admin/projects/' . $project['id'] . '/documents/') ?>' + documentId + '/delete';

    var deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
    deleteModal.show();
}
</script>

<style>
/* Mobile-friendly button design */
@media (max-width: 768px) {
    .btn-group .btn {
        padding: 0.5rem 0.75rem;
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* Required and optional field styling */
.form-control:required, .form-select:required {
    border-color: #dc3545;
}

.form-control:not(:required), .form-select:not(:required) {
    border-color: #198754;
}
</style>
<?= $this->endSection() ?>
