<?php

namespace App\Models;

/**
 * Project Document Model
 * 
 * Handles project document uploads with categorization and metadata.
 */
class ProjectDocumentModel extends BaseModel
{
    protected $table      = 'project_documents';
    protected $primaryKey = 'id';
    
    protected $allowedFields = [
        'project_id', 'milestone_id', 'doc_path', 'doc_type', 'description', 'version_no',
        'created_by', 'deleted_by'
    ];
    
    protected $useTimestamps = true;
    protected $useSoftDeletes = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $deletedField  = 'deleted_at';
    
    protected $validationRules = [
        'project_id'     => 'required|integer',
        'doc_type'       => 'permit_empty|max_length[50]',
        'description'    => 'permit_empty|max_length[255]',
        'doc_path'       => 'required|max_length[255]',
        'version_no'     => 'permit_empty|integer'
    ];
    
    protected $validationMessages = [
        'project_id' => [
            'required' => 'Project ID is required'
        ],
        'doc_path' => [
            'required' => 'Document path is required'
        ]
    ];
    
    /**
     * Get documents by project
     */
    public function getByProject(int $projectId, ?string $documentType = null): array
    {
        $query = $this->where('project_id', $projectId);

        if ($documentType) {
            $query = $query->where('doc_type', $documentType);
        }

        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Get documents by type
     */
    public function getByType(string $documentType): array
    {
        return $this->select('project_documents.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_documents.project_id')
                   ->where('project_documents.doc_type', $documentType)
                   ->orderBy('project_documents.created_at', 'DESC')
                   ->findAll();
    }
    
    /**
     * Upload document
     */
    public function uploadDocument(array $documentData): bool
    {
        $documentData['created_by'] = session()->get('admin_user_id');
        $documentData['created_at'] = date('Y-m-d H:i:s');

        return $this->insert($documentData) !== false;
    }
    
    /**
     * Delete document
     */
    public function deleteDocument(int $documentId, ?int $deletedBy = null): bool
    {
        $document = $this->find($documentId);
        
        if (!$document) {
            return false;
        }
        
        // Soft delete the record
        $result = $this->update($documentId, [
            'deleted_by' => $deletedBy,
            'deleted_at' => date('Y-m-d H:i:s')
        ]);
        
        if ($result) {
            // Delete physical file
            if (file_exists($document['doc_path'])) {
                unlink($document['doc_path']);
            }
        }
        
        return $result;
    }
    
    /**
     * Get document statistics
     */
    public function getDocumentStatistics(?int $projectId = null): array
    {
        $stats = [];

        $query = $this;
        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }

        // Total documents
        $stats['total_documents'] = $query->countAllResults();

        // Since file_size doesn't exist, set defaults
        $stats['total_size'] = 0;
        $stats['average_size'] = 0;

        // Documents by type
        $typeCounts = $this->select('doc_type, COUNT(*) as count')
                          ->where('doc_type IS NOT NULL')
                          ->groupBy('doc_type')
                          ->findAll();

        $stats['by_type'] = array_column($typeCounts, 'count', 'doc_type');

        // Recent uploads (last 30 days)
        $recentDate = date('Y-m-d', strtotime('-30 days'));
        $recentQuery = $this->where('created_at >=', $recentDate);
        if ($projectId) {
            $recentQuery = $recentQuery->where('project_id', $projectId);
        }
        $stats['recent_uploads'] = $recentQuery->countAllResults();

        return $stats;
    }
    
    /**
     * Get large documents (placeholder since file_size doesn't exist)
     */
    public function getLargeDocuments(int $sizeThreshold = 10485760): array // 10MB default
    {
        // Since file_size doesn't exist in the database, return empty array
        return [];
    }
    
    /**
     * Get recent uploads
     */
    public function getRecentUploads(int $days = 7, ?int $projectId = null): array
    {
        $dateThreshold = date('Y-m-d', strtotime("-{$days} days"));
        
        $query = $this->select('project_documents.*, projects.title as project_title, projects.pro_code')
                     ->join('projects', 'projects.id = project_documents.project_id')
                     ->where('project_documents.created_at >=', $dateThreshold);
        
        if ($projectId) {
            $query = $query->where('project_documents.project_id', $projectId);
        }
        
        return $query->orderBy('project_documents.created_at', 'DESC')->findAll();
    }
    
    /**
     * Search documents
     */
    public function searchDocuments(string $searchTerm, ?int $projectId = null): array
    {
        $query = $this->groupStart()
                     ->like('description', $searchTerm)
                     ->orLike('doc_path', $searchTerm)
                     ->orLike('doc_type', $searchTerm)
                     ->groupEnd();

        if ($projectId) {
            $query = $query->where('project_id', $projectId);
        }

        return $query->orderBy('created_at', 'DESC')->findAll();
    }
    
    /**
     * Check if file exists
     */
    public function fileExists(string $filePath): bool
    {
        return $this->where('doc_path', $filePath)->countAllResults() > 0;
    }
    
    /**
     * Get document with project info
     */
    public function getDocumentWithProject(int $documentId): ?array
    {
        return $this->select('project_documents.*, projects.title as project_title, projects.pro_code')
                   ->join('projects', 'projects.id = project_documents.project_id')
                   ->where('project_documents.id', $documentId)
                   ->first();
    }
    
    /**
     * Update document description
     */
    public function updateDescription(int $documentId, string $description): bool
    {
        return $this->update($documentId, [
            'description' => $description
        ]);
    }
    
    /**
     * Get documents count by project
     */
    public function getDocumentCountByProject(int $projectId): int
    {
        return $this->where('project_id', $projectId)->countAllResults();
    }
}
