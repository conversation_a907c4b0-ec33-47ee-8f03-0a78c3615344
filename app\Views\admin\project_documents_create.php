<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects') ?>">Projects</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects/' . $project['id']) ?>">Project Profile</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>">Project Documents</a></li>
            <li class="breadcrumb-item active" aria-current="page">Upload Document</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?= $page_title ?></h1>
            <p class="text-muted mb-0">Project: <?= esc($project['title']) ?> (<?= esc($project['pro_code']) ?>)</p>
        </div>
        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Documents
        </a>
    </div>

    <!-- Upload Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Upload Project Document</h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= esc(session()->getFlashdata('error')) ?>
                        </div>
                    <?php endif; ?>

                    <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/documents/create') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Document Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $key => $label): ?>
                                            <option value="<?= $key ?>" <?= (old('category') == $key) ? 'selected' : '' ?>>
                                                <?= esc($label) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Choose the appropriate document category (optional)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" maxlength="255"><?= old('description') ?></textarea>
                            <div class="form-text">Optional description or notes about the document (max 255 characters)</div>
                        </div>

                        <div class="mb-3">
                            <label for="document_file" class="form-label">Document File <span class="text-danger">*</span></label>
                            <input type="file" class="form-control" id="document_file" name="document_file" required>
                            <div class="form-text">
                                <strong>File Requirements:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Maximum file size: 25MB</li>
                                    <li>Allowed file types: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, ODT, ODS, ODP, JPG, JPEG, PNG, GIF, BMP, TIFF, SVG, ZIP, RAR, 7Z, TAR, GZ, CSV, XML, JSON</li>
                                    <li>Executable files (.exe, .bat) and application files are not permitted</li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Important:</strong> Once uploaded, the document will be stored securely and can be downloaded by authorized users. 
                            You can update the document details or replace the file later if needed.
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> Upload Document
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// File size validation
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const maxSize = 25 * 1024 * 1024; // 25MB in bytes
        if (file.size > maxSize) {
            alert('File size exceeds 25MB limit. Please choose a smaller file.');
            e.target.value = '';
            return;
        }
        
        // Display file info
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.remove();
        }
        
        const info = document.createElement('div');
        info.id = 'fileInfo';
        info.className = 'mt-2 p-2 bg-light rounded';
        info.innerHTML = `
            <small>
                <strong>Selected file:</strong> ${file.name}<br>
                <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                <strong>Type:</strong> ${file.type || 'Unknown'}
            </small>
        `;
        e.target.parentNode.appendChild(info);
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const file = document.getElementById('document_file').files[0];

    if (!file) {
        alert('Please select a file to upload.');
        e.preventDefault();
        return;
    }

    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';
});
</script>

<style>
/* Required and optional field styling */
.form-control:required, .form-select:required {
    border-color: #dc3545;
}

.form-control:not(:required), .form-select:not(:required) {
    border-color: #198754;
}

/* Mobile-friendly design */
@media (max-width: 768px) {
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

/* File input styling */
.form-control[type="file"] {
    padding: 0.5rem;
}

/* Alert styling */
.alert ul {
    padding-left: 1.5rem;
}
</style>
<?= $this->endSection() ?>
