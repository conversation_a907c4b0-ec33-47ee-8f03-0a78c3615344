<?= $this->extend('templates/promis_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="<?= base_url('admin/dashboard') ?>">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects') ?>">Projects</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects/' . $project['id']) ?>">Project Profile</a></li>
            <li class="breadcrumb-item"><a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>">Project Documents</a></li>
            <li class="breadcrumb-item active" aria-current="page">Edit Document</li>
        </ol>
    </nav>

    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0"><?= $page_title ?></h1>
            <p class="text-muted mb-0">Project: <?= esc($project['title']) ?> (<?= esc($project['pro_code']) ?>)</p>
        </div>
        <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Documents
        </a>
    </div>

    <!-- Edit Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Edit Project Document</h5>
                </div>
                <div class="card-body">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= esc($error) ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (session()->getFlashdata('error')): ?>
                        <div class="alert alert-danger">
                            <?= esc(session()->getFlashdata('error')) ?>
                        </div>
                    <?php endif; ?>

                    <!-- Current File Info -->
                    <div class="alert alert-info mb-4">
                        <h6><i class="fas fa-file"></i> Current File Information</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>File Path:</strong> <?= esc(basename($document['doc_path'])) ?><br>
                                <strong>Version:</strong> v<?= $document['version_no'] ?? 1 ?>
                            </div>
                            <div class="col-md-6">
                                <strong>Upload Date:</strong> <?= date('M j, Y g:i A', strtotime($document['created_at'])) ?><br>
                                <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/download') ?>"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-download"></i> Download Current File
                                </a>
                            </div>
                        </div>
                    </div>

                    <form method="POST" action="<?= base_url('admin/projects/' . $project['id'] . '/documents/' . $document['id'] . '/edit') ?>" enctype="multipart/form-data">
                        <?= csrf_field() ?>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Document Category</label>
                                    <select class="form-select" id="category" name="category">
                                        <option value="">Select Category</option>
                                        <?php foreach ($categories as $key => $label): ?>
                                            <option value="<?= $key ?>" <?= (old('category', $document['doc_type']) == $key) ? 'selected' : '' ?>>
                                                <?= esc($label) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <div class="form-text">Choose the appropriate document category (optional)</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3" maxlength="255"><?= old('description', $document['description'] ?? '') ?></textarea>
                            <div class="form-text">Optional description or notes about the document (max 255 characters)</div>
                        </div>

                        <div class="mb-3">
                            <label for="document_file" class="form-label">Replace Document File</label>
                            <input type="file" class="form-control" id="document_file" name="document_file">
                            <div class="form-text">
                                <strong>Optional:</strong> Select a new file to replace the current document. Leave empty to keep the current file.<br>
                                <strong>File Requirements:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Maximum file size: 25MB</li>
                                    <li>Allowed file types: PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX, TXT, RTF, ODT, ODS, ODP, JPG, JPEG, PNG, GIF, BMP, TIFF, SVG, ZIP, RAR, 7Z, TAR, GZ, CSV, XML, JSON</li>
                                    <li>Executable files (.exe, .bat) and application files are not permitted</li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Note:</strong> If you upload a new file, it will permanently replace the current file. 
                            The old file will be deleted from the server and cannot be recovered.
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="<?= base_url('admin/projects/' . $project['id'] . '/documents') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Document
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// File size validation
document.getElementById('document_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const maxSize = 25 * 1024 * 1024; // 25MB in bytes
        if (file.size > maxSize) {
            alert('File size exceeds 25MB limit. Please choose a smaller file.');
            e.target.value = '';
            return;
        }
        
        // Display file info
        const fileInfo = document.getElementById('fileInfo');
        if (fileInfo) {
            fileInfo.remove();
        }
        
        const info = document.createElement('div');
        info.id = 'fileInfo';
        info.className = 'mt-2 p-2 bg-light rounded';
        info.innerHTML = `
            <small>
                <strong>New file selected:</strong> ${file.name}<br>
                <strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB<br>
                <strong>Type:</strong> ${file.type || 'Unknown'}
            </small>
        `;
        e.target.parentNode.appendChild(info);
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
});
</script>

<style>
/* Required and optional field styling */
.form-control:required, .form-select:required {
    border-color: #dc3545;
}

.form-control:not(:required), .form-select:not(:required) {
    border-color: #198754;
}

/* Mobile-friendly design */
@media (max-width: 768px) {
    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
}

/* File input styling */
.form-control[type="file"] {
    padding: 0.5rem;
}

/* Alert styling */
.alert ul {
    padding-left: 1.5rem;
}
</style>
<?= $this->endSection() ?>
